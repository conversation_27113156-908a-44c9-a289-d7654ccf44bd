#include "elf_loader.h"
#include <stdio.h>
#include <stdlib.h>
#include <dlfcn.h>

int load_elf(SSL *ssl, const char *path) {
    if (!load_and_execute_elf(path)) {
        const char *error = "Failed to load ELF\n";
        tls_send(ssl, error, strlen(error));
        return -1;
    }
    else {
        const char *success = "Successfully load ELF\n";
        tls_send(ssl, success, strlen(success));
    }
    return 0;
}

bool load_and_execute_elf(const char *path) {
    void *handle = dlopen(path, RTLD_LAZY);
    if (!handle) {
        fprintf(stderr, "dlopen failed: %s\n", dlerror());
        return false;
    }

    // Find and call entry point
    void (*entry)(void) = dlsym(handle, "main");
    if (!entry) {
        fprintf(stderr, "dlsym failed: %s\n", dlerror());
        dlclose(handle);
        return false;
    }

    // Execute the loaded code
    entry();

    // Clean up
    dlclose(handle);
    return true;
}
