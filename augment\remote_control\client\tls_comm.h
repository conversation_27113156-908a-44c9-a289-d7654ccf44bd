#ifndef TLS_COMM_H
#define TLS_COMM_H

#include <openssl/ssl.h>
#include <openssl/err.h>

// Initialize TLS client context
SSL_CTX *init_tls_client();

// Connect to server with TLS
SSL *connect_to_server(SSL_CTX *ctx, const char *host, int port);

// Send encrypted data
int tls_send(SSL *ssl, const void *buf, int len);

// Receive encrypted data
int tls_recv(SSL *ssl, void *buf, int len);

#endif // TLS_COMM_H
