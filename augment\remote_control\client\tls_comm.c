#include "tls_comm.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <arpa/inet.h>

SSL_CTX *init_tls_client() {
    SSL_library_init();
    OpenSSL_add_all_algorithms();
    SSL_load_error_strings();

    SSL_CTX *ctx = SSL_CTX_new(TLS_client_method());
    if (!ctx) {
        ERR_print_errors_fp(stderr);
        return NULL;
    }

    // Configure TLS options
    SSL_CTX_set_options(ctx, SSL_OP_NO_SSLv2 | SSL_OP_NO_SSLv3 | SSL_OP_NO_COMPRESSION);
    SSL_CTX_set_verify(ctx, SSL_VERIFY_NONE, NULL); // Skip cert verification for simplicity
    
    return ctx;
}

SSL *connect_to_server(SSL_CTX *ctx, const char *host, int port) {
    // Create socket
    int sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (sockfd < 0) {
        perror("socket");
        return NULL;
    }

    // Connect to server
    struct sockaddr_in addr;
    memset(&addr, 0, sizeof(addr));
    addr.sin_family = AF_INET;
    addr.sin_port = htons(port);
    inet_pton(AF_INET, host, &addr.sin_addr);

    if (connect(sockfd, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
        perror("connect");
        close(sockfd);
        return NULL;
    }

    // Create SSL connection
    SSL *ssl = SSL_new(ctx);
    if (!ssl) {
        ERR_print_errors_fp(stderr);
        close(sockfd);
        return NULL;
    }

    SSL_set_fd(ssl, sockfd);
    if (SSL_connect(ssl) <= 0) {
        ERR_print_errors_fp(stderr);
        SSL_free(ssl);
        close(sockfd);
        return NULL;
    }

    return ssl;
}

int tls_send(SSL *ssl, const void *buf, int len) {
    int sent = SSL_write(ssl, buf, len);
    if (sent <= 0) {
        ERR_print_errors_fp(stderr);
    }
    return sent;
}

int tls_recv(SSL *ssl, void *buf, int len) {
    int received = SSL_read(ssl, buf, len);
    if (received <= 0) {
        ERR_print_errors_fp(stderr);
    }
    return received;
}
