#include "command.h"
#include <stdio.h>
#include <string.h>
#include <unistd.h>

#define BUFFER_SIZE 4096

int send_command(SSL *ssl, const char *cmd) {
    // Send command to server
    char command[BUFFER_SIZE];
    snprintf(command, sizeof(command), "CMD %s", cmd);
    if (tls_send(ssl, command, strlen(command)) <= 0) {
        return -1;
    }

    printf("Command execution started...\n");
    printf("--------------------------------\n");

    // Receive command output
    char buffer[BUFFER_SIZE];
    int received;
    while ((received = tls_recv(ssl, buffer, sizeof(buffer)-1)) > 0) {
        buffer[received] = '\0';
        
        // Check for end marker
        if (strstr(buffer, "CMD_COMPLETE")) {
            break;
        }
        
        printf("%s", buffer);
        fflush(stdout);
    }

    printf("\n--------------------------------\n");
    printf("Command execution completed\n");
    return 0;
}
