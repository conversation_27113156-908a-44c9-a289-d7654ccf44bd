#include "sysinfo.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/utsname.h>

#define INFO_BUFFER_SIZE 8192

int get_system_info(SSL *ssl) {
    char info[INFO_BUFFER_SIZE] = {0};
    char temp[256];
    FILE *fp;
    
    // Get system name and version
    struct utsname uts;
    uname(&uts);
    snprintf(info, sizeof(info), 
             "System Information:\n"
             "------------------\n"
             "System Name: %s\n"
             "Node Name: %s\n"
             "Release: %s\n"
             "Version: %s\n"
             "Machine: %s\n\n",
             uts.sysname, uts.nodename, uts.release, 
             uts.version, uts.machine);

    // Get CPU info
    fp = fopen("/proc/cpuinfo", "r");
    if (fp) {
        size_t remaining = INFO_BUFFER_SIZE - strlen(info) - 1;
        if (remaining > 0) {
            strncat(info, "CPU Information:\n------------------\n", remaining);
        }
        while (fgets(temp, sizeof(temp), fp)) {
            if (strstr(temp, "model name") || strstr(temp, "cpu cores")) {
                remaining = INFO_BUFFER_SIZE - strlen(info) - 1;
                if (remaining > 0) {
                    strncat(info, temp, remaining);
                } else {
                    break;
                }
            }
        }
        fclose(fp);
        remaining = INFO_BUFFER_SIZE - strlen(info) - 1;
        if (remaining > 0) {
            strncat(info, "\n", remaining);
        }
    }

    // Get memory info
    fp = fopen("/proc/meminfo", "r");
    if (fp) {
        size_t remaining = INFO_BUFFER_SIZE - strlen(info) - 1;
        if (remaining > 0) {
            strncat(info, "Memory Information:\n------------------\n", remaining);
        }
        while (fgets(temp, sizeof(temp), fp)) {
            if (strstr(temp, "MemTotal") || strstr(temp, "MemFree") || 
                strstr(temp, "MemAvailable")) {
                remaining = INFO_BUFFER_SIZE - strlen(info) - 1;
                if (remaining > 0) {
                    strncat(info, temp, remaining);
                } else {
                    break;
                }
            }
        }
        fclose(fp);
        remaining = INFO_BUFFER_SIZE - strlen(info) - 1;
        if (remaining > 0) {
            strncat(info, "\n", remaining);
        }
    }

    // Get uptime
    fp = fopen("/proc/uptime", "r");
    if (fp) {
        double uptime;
        fscanf(fp, "%lf", &uptime);
        fclose(fp);
        
        int days = (int)(uptime / 86400);
        int hours = (int)((uptime - days * 86400) / 3600);
        int minutes = (int)((uptime - days * 86400 - hours * 3600) / 60);
        
        snprintf(temp, sizeof(temp), 
                "Uptime: %d days, %d hours, %d minutes\nCMD_COMPLETE",
                days, hours, minutes);
        strcat(info, temp);
    }

    // Send collected info
    if (tls_send(ssl, info, strlen(info)) <= 0) {
        return -1;
    }

    return 0;
}
