#include "file_transfer.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>

int receive_file(SSL *ssl, const char *path, long file_size) {
    FILE *file = fopen(path, "wb");
    if (!file) {
        perror("fopen");
        return -1;
    }

    char buffer[CHUNK_SIZE];
    long total_received = 0;
    
    while (total_received < file_size) {
        int to_read = (file_size - total_received) > sizeof(buffer) ? 
                     sizeof(buffer) : (file_size - total_received);
        int received = tls_recv(ssl, buffer, to_read);
        if (received <= 0) {
            fclose(file);
            remove(path);
            return -1;
        }
        
        fwrite(buffer, 1, received, file);
        total_received += received;
        printf("Receiving: %.2f%%\r", (float)total_received/file_size*100);
        fflush(stdout);
    }

    fclose(file);
    printf("\nFile received: %s (%ld bytes)\n", path, file_size);
    return 0;
}

int send_file(SSL *ssl, const char *path) {
    FILE *file = fopen(path, "rb");
    if (!file) {
        perror("fopen");
        return -1;
    }

    // Get file size
    struct stat st;
    stat(path, &st);
    long file_size = st.st_size;

    // Send file info
    char header[1024];
    snprintf(header, sizeof(header), "FILE %ld", file_size);
    if (tls_send(ssl, header, strlen(header)) <= 0) {
        fclose(file);
        return -1;
    }

    // Send file content
    char buffer[CHUNK_SIZE];
    size_t bytes_read;
    long total_sent = 0;
    
    while ((bytes_read = fread(buffer, 1, sizeof(buffer), file)) > 0) {
        int sent = tls_send(ssl, buffer, bytes_read);
        if (sent <= 0) {
            fclose(file);
            return -1;
        }
        total_sent += sent;
        printf("Sending: %.2f%%\r", (float)total_sent/file_size*100);
        fflush(stdout);
    }

    fclose(file);
    printf("\nFile sent: %s (%ld bytes)\n", path, file_size);
    return 0;
}
