#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>

// Entry point function that will be called by the ELF loader
void main(void) {
    FILE *file;
    time_t current_time;
    struct tm *time_info;
    char time_string[100];
    char content[200];
    char filename[50];
    
    // Get current time
    time(&current_time);
    time_info = localtime(&current_time);
    strftime(time_string, sizeof(time_string), "%Y-%m-%d %H:%M:%S", time_info);
    
    // Create filename with timestamp
    snprintf(filename, sizeof(filename), "plugin_test_%ld.txt", current_time);
    
    // Prepare content
    snprintf(content, sizeof(content), "插件加载测试+当前日期: %s\n", time_string);
    
    // Create and write to file
    file = fopen(filename, "w");
    if (file == NULL) {
        fprintf(stderr, "Error: Cannot create file %s\n", filename);
        return;
    }
    
    fprintf(file, "%s", content);
    fclose(file);
    
    // Print success message to stdout
    printf("Test plugin executed successfully!\n");
    printf("Created file: %s\n", filename);
    printf("Content: %s", content);
    
    // Also try to create a simple log file
    file = fopen("plugin_execution.log", "a");
    if (file != NULL) {
        fprintf(file, "[%s] Test plugin executed, created file: %s\n", 
                time_string, filename);
        fclose(file);
    }
}
