#ifndef TLS_COMM_SERVER_H
#define TLS_COMM_SERVER_H

#include <openssl/ssl.h>
#include <openssl/err.h>

// Initialize TLS server context
SSL_CTX *init_tls_server();

// Create server socket
int create_server_socket(int port);

// Accept client connection
SSL *accept_client_connection(SSL_CTX *ctx, int sockfd);

// Send encrypted data
int tls_send(SSL *ssl, const void *buf, int len);

// Receive encrypted data
int tls_recv(SSL *ssl, void *buf, int len);

#endif // TLS_COMM_SERVER_H
