#include "tls_comm.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <arpa/inet.h>

SSL_CTX *init_tls_server() {
    SSL_library_init();
    OpenSSL_add_all_algorithms();
    SSL_load_error_strings();

    SSL_CTX *ctx = SSL_CTX_new(TLS_server_method());
    if (!ctx) {
        ERR_print_errors_fp(stderr);
        return NULL;
    }

    // Configure TLS options
    SSL_CTX_set_options(ctx, SSL_OP_NO_SSLv2 | SSL_OP_NO_SSLv3 | SSL_OP_NO_COMPRESSION);
    SSL_CTX_set_verify(ctx, SSL_VERIFY_NONE, NULL); // Skip client cert verification
    
    return ctx;
}

int create_server_socket(int port) {
    int sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (sockfd < 0) {
        perror("socket");
        return -1;
    }

    // Set SO_REUSEADDR
    int opt = 1;
    setsockopt(sockfd, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));

    // Bind socket
    struct sockaddr_in addr;
    memset(&addr, 0, sizeof(addr));
    addr.sin_family = AF_INET;
    addr.sin_port = htons(port);
    addr.sin_addr.s_addr = INADDR_ANY;

    if (bind(sockfd, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
        perror("bind");
        close(sockfd);
        return -1;
    }

    // Listen for connections
    if (listen(sockfd, 5) < 0) {
        perror("listen");
        close(sockfd);
        return -1;
    }

    return sockfd;
}

SSL *accept_client_connection(SSL_CTX *ctx, int sockfd) {
    // Accept TCP connection
    struct sockaddr_in addr;
    socklen_t len = sizeof(addr);
    int client_fd = accept(sockfd, (struct sockaddr*)&addr, &len);
    if (client_fd < 0) {
        perror("accept");
        return NULL;
    }

    printf("Client connected: %s:%d\n", 
           inet_ntoa(addr.sin_addr), ntohs(addr.sin_port));

    // Create SSL connection
    SSL *ssl = SSL_new(ctx);
    if (!ssl) {
        ERR_print_errors_fp(stderr);
        close(client_fd);
        return NULL;
    }

    SSL_set_fd(ssl, client_fd);
    if (SSL_accept(ssl) <= 0) {
        ERR_print_errors_fp(stderr);
        SSL_free(ssl);
        close(client_fd);
        return NULL;
    }

    return ssl;
}

int tls_send(SSL *ssl, const void *buf, int len) {
    int sent = SSL_write(ssl, buf, len);
    if (sent <= 0) {
        ERR_print_errors_fp(stderr);
    }
    return sent;
}

int tls_recv(SSL *ssl, void *buf, int len) {
    int received = SSL_read(ssl, buf, len);
    if (received <= 0) {
        ERR_print_errors_fp(stderr);
    }
    return received;
}
