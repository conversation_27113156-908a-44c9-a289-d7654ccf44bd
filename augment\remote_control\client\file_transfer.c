#include "file_transfer.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>

int upload_file(SSL *ssl, const char *local_path, const char *remote_path) {
    FILE *file = fopen(local_path, "rb");
    if (!file) {
        perror("fopen");
        return -1;
    }

    // Get file size
    struct stat st;
    stat(local_path, &st);
    long file_size = st.st_size;

    // Send upload command and file info
    char header[1024];
    snprintf(header, sizeof(header), "UPLOAD %s %ld", remote_path, file_size);
    if (tls_send(ssl, header, strlen(header)) <= 0) {
        fclose(file);
        return -1;
    }

    // Send file content
    char buffer[CHUNK_SIZE];
    size_t bytes_read;
    long total_sent = 0;
    
    while ((bytes_read = fread(buffer, 1, sizeof(buffer), file)) > 0) {
        int sent = tls_send(ssl, buffer, bytes_read);
        if (sent <= 0) {
            fclose(file);
            return -1;
        }
        total_sent += sent;
        printf("Upload progress: %.2f%%\r", (float)total_sent/file_size*100);
        fflush(stdout);
    }

    fclose(file);
    printf("\nUpload completed: %s -> %s\n", local_path, remote_path);
    return 0;
}

int download_file(SSL *ssl, const char *remote_path, const char *local_path) {
    // Send download command
    char command[1024];
    snprintf(command, sizeof(command), "DOWNLOAD %s", remote_path);
    if (tls_send(ssl, command, strlen(command)) <= 0) {
        return -1;
    }

    // Receive file info
    char response[1024];
    int len = tls_recv(ssl, response, sizeof(response)-1);
    if (len <= 0) {
        return -1;
    }
    response[len] = '\0';

    long file_size;
    if (sscanf(response, "FILE %ld", &file_size) != 1) {
        fprintf(stderr, "Invalid server response\n");
        return -1;
    }

    // Receive file content
    FILE *file = fopen(local_path, "wb");
    if (!file) {
        perror("fopen");
        return -1;
    }

    char buffer[CHUNK_SIZE];
    long total_received = 0;
    
    while (total_received < file_size) {
        int to_read = (file_size - total_received) > sizeof(buffer) ? 
                     sizeof(buffer) : (file_size - total_received);
        int received = tls_recv(ssl, buffer, to_read);
        if (received <= 0) {
            fclose(file);
            remove(local_path);
            return -1;
        }
        
        fwrite(buffer, 1, received, file);
        total_received += received;
        printf("Download progress: %.2f%%\r", (float)total_received/file_size*100);
        fflush(stdout);
    }

    fclose(file);
    printf("\nDownload completed: %s -> %s\n", remote_path, local_path);
    return 0;
}
