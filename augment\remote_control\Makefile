CC = gcc
CFLAGS = -Wall -Wextra -I. -static
LDFLAGS = -lssl -lcrypto -ldl -lpthread -lz -lzstd -static

CLIENT_SRC = client/main.c client/tls_comm.c client/file_transfer.c \
             client/command.c
CLIENT_OBJ = $(CLIENT_SRC:.c=.o)
CLIENT_TARGET = remote_client

SERVER_SRC = server/main.c server/tls_comm.c server/file_transfer.c \
             server/command.c server/elf_loader.c server/sysinfo.c
SERVER_OBJ = $(SERVER_SRC:.c=.o)
SERVER_TARGET = remote_server

# Test plugin
TEST_PLUGIN_SRC = test_plugin.c
TEST_PLUGIN_TARGET = test_plugin.so

all: $(CLIENT_TARGET) $(SERVER_TARGET) $(TEST_PLUGIN_TARGET)

$(CLIENT_TARGET): $(CLIENT_OBJ)
	$(CC) $(CFLAGS) -o $@ $^ $(LDFLAGS)

$(SERVER_TARGET): $(SERVER_OBJ)
	$(CC) $(CFLAGS) -o $@ $^ $(LDFLAGS)

%.o: %.c
	$(CC) $(CFLAGS) -c -o $@ $<

clean:
	rm -f $(CLIENT_OBJ) $(SERVER_OBJ) $(CLIENT_TARGET) $(SERVER_TARGET)

.PHONY: all clean
