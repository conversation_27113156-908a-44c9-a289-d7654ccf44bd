#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "tls_comm.h"
#include "file_transfer.h"
#include "command.h"

int main(int argc, char *argv[]) {
    if (argc != 3) {
        printf("Usage: %s <server_ip> <server_port>\n", argv[0]);
        return 1;
    }

    // Initialize TLS connection
    SSL_CTX *ctx = init_tls_client();
    if (!ctx) {
        fprintf(stderr, "TLS initialization failed\n");
        return 1;
    }

    SSL *ssl = connect_to_server(ctx, argv[1], atoi(argv[2]));
    if (!ssl) {
        fprintf(stderr, "Connection failed\n");
        SSL_CTX_free(ctx);
        return 1;
    }

    // Main loop
    while (1) {
        printf("\nRemote Control Menu:\n");
        printf("1. Execute command\n");
        printf("2. Upload file\n");
        printf("3. Download file\n");
        printf("4. Get system info\n");
        printf("5. Load ELF executable\n");
        printf("6. Exit\n");
        printf("Choice: ");

        int choice;
        scanf("%d", &choice);

        switch (choice) {
            case 1: {
                char cmd[1024];
                printf("Enter command: ");
                scanf(" %[^\n]", cmd);
                send_command(ssl, cmd);
                break;
            }
            case 2: {
                char local_path[256], remote_path[256];
                printf("Local file path: ");
                scanf("%s", local_path);
                printf("Remote file path: ");
                scanf("%s", remote_path);
                upload_file(ssl, local_path, remote_path);
                break;
            }
            case 3: {
                char remote_path[256], local_path[256];
                printf("Remote file path: ");
                scanf("%s", remote_path);
                printf("Local file path: ");
                scanf("%s", local_path);
                download_file(ssl, remote_path, local_path);
                break;
            }
            case 4: {
                // Send SYSINFO command to server
                const char *sysinfo_cmd = "SYSINFO\n";
                if (tls_send(ssl, sysinfo_cmd, strlen(sysinfo_cmd)) <= 0) {
                    fprintf(stderr, "Failed to send SYSINFO command\n");
                    break;
                }

                // Receive and display system info
                char buffer[4096];
                int received;
                while ((received = tls_recv(ssl, buffer, sizeof(buffer)-1)) > 0) {

                    buffer[received] = '\0';
                    
                    printf("%s", buffer);
                    fflush(stdout);
                            // Check for end marker
                    if (strstr(buffer, "CMD_COMPLETE")) {
                        break;
                    }
                }
                printf("\n--------------------------------\n");
                break;
            }
            case 5: {
                char elf_path[256];
                printf("ELF file path to load: ");
                scanf("%s", elf_path);
                
                // Send LOAD_ELF command
                char command[512];
                snprintf(command, sizeof(command), "LOAD_ELF %s", elf_path);
                if (tls_send(ssl, command, strlen(command)) <= 0) {
                    fprintf(stderr, "Failed to send LOAD_ELF command\n");
                    break;
                }

                // Receive execution output
                char buffer[4096];
                int received;
                while ((received = tls_recv(ssl, buffer, sizeof(buffer)-1)) > 0) {
                    buffer[received] = '\0';
                    printf("%s", buffer);
                    fflush(stdout);
                    if (strstr(buffer, "ELF")) {
                        break;
                    }
                }
                break;
            }
            case 6: {
                SSL_shutdown(ssl);
                SSL_free(ssl);
                SSL_CTX_free(ctx);
                return 0;
            }
            default:
                printf("Invalid choice\n");
        }
    }

    return 0;
}
